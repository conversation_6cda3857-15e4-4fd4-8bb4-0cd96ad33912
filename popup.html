<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Time Machines</title>
    <link rel="stylesheet" href="css/review-analysis.css">
    <link rel="stylesheet" href="css/popup.css">
    <link rel="stylesheet" href="css/location-changer.css">
    <link rel="stylesheet" href="css/pomodoro.css">
    <style>
      .twitter-typeahead {
        width: 100%;
        flex: 1 1 auto;
      }
      .tt-menu {
        width: 100%;
      }
      .tt-menu,.typeahead {
        background-color: #1a1a1a;
      }
      .tt-suggestion {
        cursor: default;
        padding: 10px 16px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 20px;
        text-align: left;
        border-top: 1px solid #2a2a2a;
        margin-bottom: 0;
        color: #e5e5e5;
      }
      .text-danger::placeholder {
        color: #ef4444!important;
      }
      
      /* Quick Actions Layout Styles */
      .quick-actions-layout {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        width: 100%;
        gap: 15px;
        min-height: 60px;
      }
      
      .quick-actions-left {
        flex: 1;
      }
      
      .quick-actions-right {
        display: flex;
        align-items: flex-end;
        align-self: flex-end;
      }
      
      .youtube-scraper-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px;
      }
      
      .youtube-scraper-icon {
        cursor: pointer;
        border-radius: 6px;
        padding: 6px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
      
      .youtube-scraper-icon:hover {
        background: rgba(255, 0, 0, 0.1);
        border-color: rgba(255, 0, 0, 0.3);
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(255, 0, 0, 0.2);
      }
      
      .youtube-scraper-icon:active {
        transform: scale(0.95);
      }
      
      .youtube-scraper-icon svg {
        transition: all 0.2s ease;
      }
      
      .youtube-scraper-icon:hover svg {
        filter: drop-shadow(0 0 4px rgba(255, 0, 0, 0.5));
      }
      
      /* Header Subtitle Styles */
      .header__subtitle {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: #888;
        font-weight: normal;
        line-height: 1.2;
        opacity: 0.8;
      }
      
      /* UTM Whitelist Button Styles */
      .btn--whitelist {
        background: rgba(136, 136, 136, 0.1) !important;
        border: 1px solid rgba(136, 136, 136, 0.3) !important;
        color: #888888 !important;
        transition: all 0.2s ease !important;
        padding: 4px 6px !important;
        margin-left: 4px !important;
      }
      
      .btn--whitelist:hover {
        background: rgba(124, 58, 237, 0.15) !important;
        border-color: rgba(124, 58, 237, 0.4) !important;
        color: #7C3AED !important;
        transform: scale(1.05) !important;
        box-shadow: 0 2px 8px rgba(124, 58, 237, 0.2) !important;
      }
      
      .btn--whitelist:active {
        transform: scale(0.95) !important;
        background: rgba(124, 58, 237, 0.25) !important;
      }
      
      .btn--whitelist svg {
        transition: all 0.2s ease !important;
      }
      
      .btn--whitelist:hover svg path {
        fill: #7C3AED !important;
      }
      
      .btn--whitelist:hover svg {
        filter: drop-shadow(0 0 3px rgba(124, 58, 237, 0.4)) !important;
      }
      
      .btn--whitelist.added {
        background: rgba(34, 197, 94, 0.15) !important;
        border-color: rgba(34, 197, 94, 0.4) !important;
        color: #22c55e !important;
      }
      
      .btn--whitelist.added svg path {
        fill: #22c55e !important;
      }
      
      .btn--whitelist.added:hover {
        background: rgba(34, 197, 94, 0.25) !important;
        border-color: rgba(34, 197, 94, 0.6) !important;
      }
      
      /* Pomodoro Toggle Button Styles */
      .btn--pomodoro {
        background: rgba(136, 136, 136, 0.1) !important;
        border: 1px solid rgba(136, 136, 136, 0.3) !important;
        color: #888888 !important;
        transition: all 0.2s ease !important;
        padding: 4px 6px !important;
        margin-left: 4px !important;
      }
      
      .btn--pomodoro:hover {
        background: rgba(124, 58, 237, 0.15) !important;
        border-color: rgba(124, 58, 237, 0.4) !important;
        color: #7C3AED !important;
        transform: scale(1.05) !important;
        box-shadow: 0 2px 8px rgba(124, 58, 237, 0.2) !important;
      }
      
      .btn--pomodoro:active {
        transform: scale(0.95) !important;
        background: rgba(124, 58, 237, 0.25) !important;
      }
      
      .btn--pomodoro svg {
        transition: all 0.2s ease !important;
      }
      
      .btn--pomodoro:hover svg path {
        fill: #7C3AED !important;
      }
      
      .btn--pomodoro:hover svg {
        filter: drop-shadow(0 0 3px rgba(124, 58, 237, 0.4)) !important;
      }
      
      .btn--pomodoro.active {
        background: rgba(239, 68, 68, 0.15) !important;
        border-color: rgba(239, 68, 68, 0.4) !important;
        color: #ef4444 !important;
      }
      
      .btn--pomodoro.active svg path {
        fill: #ef4444 !important;
      }
      
      .btn--pomodoro.active:hover {
        background: rgba(239, 68, 68, 0.25) !important;
        border-color: rgba(239, 68, 68, 0.6) !important;
      }
      
      /* Alert Button Styles */
      .btn--alert {
        background: rgba(136, 136, 136, 0.1) !important;
        border: 1px solid rgba(136, 136, 136, 0.3) !important;
        color: #888888 !important;
        transition: all 0.2s ease !important;
        padding: 4px 6px !important;
        margin-left: 4px !important;
        position: relative !important;
      }
      
      .btn--alert:hover {
        background: rgba(124, 58, 237, 0.15) !important;
        border-color: rgba(124, 58, 237, 0.4) !important;
        color: #7C3AED !important;
        transform: scale(1.05) !important;
        box-shadow: 0 2px 8px rgba(124, 58, 237, 0.2) !important;
      }
      
      .btn--alert:active {
        transform: scale(0.95) !important;
        background: rgba(124, 58, 237, 0.25) !important;
      }
      
      .btn--alert svg {
        transition: all 0.2s ease !important;
      }
      
      .btn--alert:hover svg path {
        fill: #7C3AED !important;
      }
      
      .btn--alert:hover svg {
        filter: drop-shadow(0 0 3px rgba(124, 58, 237, 0.4)) !important;
      }
      
      .btn--alert.has-alerts {
        background: rgba(251, 191, 36, 0.15) !important;
        border-color: rgba(251, 191, 36, 0.4) !important;
        color: #fbbf24 !important;
      }
      
      .btn--alert.has-alerts svg path {
        fill: #fbbf24 !important;
      }
      
      .btn--alert.has-alerts:hover {
        background: rgba(251, 191, 36, 0.25) !important;
        border-color: rgba(251, 191, 36, 0.6) !important;
      }
      
      /* Alert Badge Styles */
      .alert-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        background: #ef4444;
        color: white;
        font-size: 9px;
        font-weight: bold;
        padding: 1px 4px;
        border-radius: 10px;
        min-width: 14px;
        text-align: center;
        line-height: 1.2;
        box-shadow: 0 1px 3px rgba(0,0,0,0.3);
      }
      
      /* Email Pinner Button Styles */
      .btn--email-pinner {
        background: rgba(136, 136, 136, 0.1) !important;
        border: 1px solid rgba(136, 136, 136, 0.3) !important;
        color: #888888 !important;
        transition: all 0.2s ease !important;
        padding: 4px 6px !important;
        margin-left: 4px !important;
        position: relative !important;
      }
      
      .btn--email-pinner:hover {
        background: rgba(124, 58, 237, 0.15) !important;
        border-color: rgba(124, 58, 237, 0.4) !important;
        color: #7C3AED !important;
        transform: scale(1.05) !important;
        box-shadow: 0 2px 8px rgba(124, 58, 237, 0.2) !important;
      }
      
      .btn--email-pinner:active {
        transform: scale(0.95) !important;
        background: rgba(124, 58, 237, 0.25) !important;
      }
      
      .btn--email-pinner svg {
        transition: all 0.2s ease !important;
      }
      
      .btn--email-pinner:hover svg path {
        fill: #7C3AED !important;
      }
      
      .btn--email-pinner:hover svg {
        filter: drop-shadow(0 0 3px rgba(124, 58, 237, 0.4)) !important;
      }
      
      .btn--email-pinner.has-emails {
        background: rgba(251, 191, 36, 0.15) !important;
        border-color: rgba(251, 191, 36, 0.4) !important;
        color: #fbbf24 !important;
      }
      
      .btn--email-pinner.has-emails svg path {
        fill: #fbbf24 !important;
      }
      
      .btn--email-pinner.has-emails:hover {
        background: rgba(251, 191, 36, 0.25) !important;
        border-color: rgba(251, 191, 36, 0.6) !important;
      }
      
      /* Email Pinner Badge Styles */
      .email-pinner-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        background: #7C3AED;
        color: white;
        font-size: 9px;
        font-weight: bold;
        padding: 1px 4px;
        border-radius: 10px;
        min-width: 14px;
        text-align: center;
        line-height: 1.2;
        box-shadow: 0 1px 3px rgba(0,0,0,0.3);
      }
      
      /* Quick Timer Styles */
      .quick-timer-section {
        background: #1a1a1a;
        border: 1px solid #333;
        border-radius: 8px;
        margin: 15px 0;
      }
      
      .timer-display {
        font-size: 48px;
        font-weight: 300;
        color: #fbbf24;
        text-align: center;
        margin-bottom: 15px;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
        letter-spacing: 2px;
      }
      
      .timer-display span {
        display: inline-block;
      }
      
      .timer-presets {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
        margin-bottom: 15px;
      }
      
      .timer-preset {
        position: relative;
        padding: 8px 24px 8px 8px;
        background: #0a0a0a;
        border: 1px solid #444;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
      }
      
      .timer-preset:hover {
        border-color: #7C3AED;
        color: #fff;
        transform: translateY(-1px);
      }
      
      .timer-preset:active {
        transform: translateY(0);
      }
      
      .timer-preset-text {
        display: block;
        width: 100%;
      }
      
      .timer-start-btn {
        width: 100%;
        padding: 12px 16px;
        background: #7C3AED;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
      }
      
      .timer-start-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
      }
      
      .timer-start-btn:hover::before {
        left: 100%;
      }
      
      .timer-start-btn:hover {
        background: #6d28d9;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
      }
      
      .timer-start-btn:active {
        transform: translateY(0);
      }
      
      .timer-start-btn.active {
        background: #dc2626;
      }
      
      .timer-start-btn.active:hover {
        background: #b91c1c;
      }
      
      /* Custom Duration Input Styles */
      .timer-custom-input {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;
        align-items: center;
      }
      
      .custom-duration-input {
        flex: 1;
        padding: 8px 12px;
        background: #0a0a0a;
        border: 1px solid #444;
        border-radius: 4px;
        color: #e5e5e5;
        font-size: 14px;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
      }
      
      .custom-duration-input:focus {
        outline: none;
        border-color: #7C3AED;
        box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
      }
      
      .timer-custom-set-btn {
        padding: 8px 16px;
        background: #7C3AED;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .timer-custom-set-btn:hover {
        background: #6d28d9;
        transform: translateY(-1px);
      }
      
      .timer-preset-custom {
        background: rgba(124, 58, 237, 0.1) !important;
        border-color: #7C3AED !important;
        color: #7C3AED !important;
      }
      
      .timer-preset-custom:hover {
        background: rgba(124, 58, 237, 0.2) !important;
        color: #7C3AED !important;
      }
      
      /* Plus Button Styles - Overlaid within button */
      .timer-preset-plus {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 21px;
        height: 21px;
        padding: 0;
        background: #7C3AED;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 11px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
        z-index: 2;
        pointer-events: auto;
      }
      
      .timer-preset-plus:hover {
        background: #6d28d9;
        transform: scale(1.1);
        box-shadow: 0 2px 6px rgba(124, 58, 237, 0.4);
      }
      
      .timer-preset-plus:active {
        transform: scale(0.95);
        background: #5b21b6;
      }
      
      /* Add visual feedback for successful time addition */
      .timer-display.time-added {
        animation: timeAddedPulse 0.6s ease-out;
      }
      
      @keyframes timeAddedPulse {
        0% { transform: scale(1); color: #fbbf24; }
        50% { transform: scale(1.05); color: #7C3AED; }
        100% { transform: scale(1); color: #fbbf24; }
      }
      
      /* Header Button Container - Contains buttons and timer */
      .header__button-container {
        position: relative;
        display: flex;
        align-items: center;
        gap: 6px;
      }
      
      /* Header Timer Display Styles - Absolute positioned overlay */
      .header-timer-display {
        position: absolute;
        top: 100%;
        right: 0;
        margin-top: 4px;
        font-size: 16px;
        font-weight: 600;
        color: #fbbf24;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
        letter-spacing: 0.5px;
        padding: 2px 6px;
        background: rgba(26, 26, 26, 0.9);
        border-radius: 4px;
        border: 1px solid rgba(251, 191, 36, 0.3);
        text-align: right;
        display: flex;
        justify-content: flex-end;
        white-space: nowrap;
        z-index: 10;
        margin-top: 12px;
      }
      
      .header-timer-display span {
        display: inline-block;
      }
      
      /* Quick Timer Accordion Styles - Match Tasks Section Exactly */
      .quick-timer-section {
        background: rgba(124, 58, 237, 0.05);
        border: 1px solid rgba(124, 58, 237, 0.2);
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;
        transition: all 0.3s ease;
      }
      
      .quick-timer-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        cursor: pointer;
        background: rgba(124, 58, 237, 0.1);
        border-bottom: 1px solid rgba(124, 58, 237, 0.2);
        transition: all 0.2s ease;
      }
      
      .quick-timer-header:hover {
        background: rgba(124, 58, 237, 0.15);
      }
      
      .quick-timer-header.expanded {
        background: rgba(124, 58, 237, 0.2);
      }
      
      .quick-timer-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 600;
        color: #d1d5db;
      }
      
      .quick-timer-status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #7C3AED;
        animation: pulse 2s infinite;
      }
      
      .quick-timer-icon {
        font-size: 12px;
        color: #7C3AED;
        transition: transform 0.2s ease;
      }
      
      .quick-timer-header.expanded .quick-timer-icon {
        transform: rotate(180deg);
      }
      
      .quick-timer-content {
        padding: 0;
        background: transparent;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
      }
      
      .quick-timer-content.expanded {
        max-height: 600px;
      }
      
      /* Timer Controls Form - Individual padding like tasks-add-form */
      .timer-controls-form {
        padding: 16px;
      }
      
      /* Pomodoro Section - Match accordion styling */
      .pomodoro-section {
        background: rgba(124, 58, 237, 0.05);
        border: 1px solid rgba(124, 58, 237, 0.2);
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;
        transition: all 0.3s ease;
      }
      
      /* Header Button Tooltips */
      .header__button-group .btn--mini {
        position: relative;
      }
      
      .header__button-group .btn--mini::before {
        content: attr(data-shortcut);
        position: absolute;
        top: -35px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.9);
        color: #d1d5db;
        padding: 6px 8px;
        border-radius: 6px;
        font-size: 11px;
        font-weight: 500;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
        transition-delay: 0s;
        pointer-events: none;
        z-index: 1000;
        border: 1px solid #7C3AED;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
      
      .header__button-group .btn--mini:hover::before {
        opacity: 1;
        visibility: visible;
        transition-delay: 1s;
      }
      
      /* Hide tooltips when shortcuts are disabled */
      body.popup-shortcuts-disabled .header__button-group .btn--mini:hover::before {
        opacity: 0 !important;
        visibility: hidden !important;
      }
    </style>
</head>
<body class="popup">
    <div class="popup__container">
        <header class="header">
            <img src="images/icon128.png" alt="STM Logo" class="header__logo">
            <div class="header__content">
                <div class="header__title-row">
                    <h1 class="header__title">SEO Time Machines</h1>
                    <div class="header__button-container">
                        <div class="header__button-group">
                            <button class="btn--mini btn--reload" id="reloadBtn" title="Reload Extension" data-shortcut="Press r">
                                <svg width="12" height="12" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M234.666667,149.333333 L234.666667,106.666667 L314.564847,106.664112 C287.579138,67.9778918 242.745446,42.6666667 192,42.6666667 C109.525477,42.6666667 42.6666667,109.525477 42.6666667,192 C42.6666667,274.474523 109.525477,341.333333 192,341.333333 C268.201293,341.333333 331.072074,284.258623 340.195444,210.526102 L382.537159,215.817985 C370.807686,310.617565 289.973536,384 192,384 C85.961328,384 1.42108547e-14,298.038672 1.42108547e-14,192 C1.42108547e-14,85.961328 85.961328,1.42108547e-14 192,1.42108547e-14 C252.316171,1.42108547e-14 306.136355,27.8126321 341.335366,71.3127128 L341.333333,1.42108547e-14 L384,1.42108547e-14 L384,149.333333 L234.666667,149.333333 Z" transform="translate(64.000000, 64.000000)" fill="currentColor"/>
                                </svg>
                            </button>
                            <button class="btn--mini" id="settingsBtn" style="display: none;" data-shortcut="Press s">⚙️ Settings</button>
                            <button class="btn--mini btn--whitelist" id="utmWhitelistBtn" style="display: none;" title="Add current site to UTM cleaner whitelist">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9ZM19 21H5V3H13V9H19V21ZM8 19H16V17H8V19ZM8 15H16V13H8V15ZM8 11H16V9H8V11Z" fill="#888888"/>
                                    <circle cx="18" cy="6" r="3" fill="#7C3AED"/>
                                    <path d="M16.5 6L17.5 7L19.5 5" stroke="white" stroke-width="1" fill="none"/>
                                </svg>
                            </button>
                            <button class="btn--mini btn--pomodoro" id="pomodoroToggleBtn" style="display: none;" title="Start/Stop Pomodoro Timer" data-shortcut="Press t">
                                <svg width="14" height="14" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                                    <path fill="currentColor" d="M9.060 9.060c0.271-0.271 0.439-0.646 0.439-1.060s-0.168-0.789-0.439-1.060c-0.59-0.59-6.72-4.6-6.72-4.6s4 6.13 4.59 6.72c0.272 0.274 0.649 0.444 1.065 0.444s0.793-0.17 1.065-0.444z"></path>
                                    <path fill="currentColor" d="M8 0v3h1v-1.41c3.153 0.495 5.536 3.192 5.536 6.445 0 3.601-2.919 6.52-6.52 6.52s-6.52-2.919-6.52-6.52c0-1.256 0.355-2.428 0.97-3.423l-0.916-1.322c-0.958 1.303-1.533 2.939-1.533 4.71 0 4.418 3.582 8 8 8s8-3.582 8-8c0-4.418-3.582-8-8-8-0.006 0-0.012 0-0.017 0z"></path>
                                </svg>
                            </button>
                            <button class="btn--mini btn--alert" id="alertToggleBtn" style="display: none;" title="Manage Alerts & Reminders" data-shortcut="Press a">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C10.9 2 10 2.9 10 4C10 4.1 10 4.2 10 4.29C7.12 5.14 5 7.82 5 11V17L3 19V20H21V19L19 17V11C19 7.82 16.88 5.14 14 4.29C14 4.2 14 4.1 14 4C14 2.9 13.1 2 12 2ZM12 22C13.11 22 14 21.11 14 20H10C10 21.11 10.89 22 12 22Z" fill="currentColor"/>
                                </svg>
                                <span class="alert-badge" id="alertBadge" style="display: none;">0</span>
                            </button>
                            <button class="btn--mini btn--email-pinner" id="emailPinnerBtn" style="display: none;" title="Manage Pinned Emails" data-shortcut="Press e">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" fill="currentColor"/>
                                </svg>
                                <span class="email-pinner-badge" id="emailPinnerBadge" style="display: none;">0</span>
                            </button>
                        </div>
                        
                        <!-- Timer Display Inside Button Container -->
                        <div class="header-timer-display" id="headerTimerDisplay" style="display: none;">
                            <span class="header-timer-hours">00</span>:<span class="header-timer-minutes">00</span>:<span class="header-timer-seconds">00</span>
                        </div>
                    </div>
                </div>
                <p style="text-align: left;" class="header__subtitle">Blazing fast competitor analysis and web dev toolkit<button id="secretDebugToggle" style="background: transparent; border: none; color: #888; font-size: 12px; padding: 0; margin: 0; cursor: default; opacity: 0.8; line-height: 1; font-family: inherit;">.</button></p>
                <div class="header__status-wrapper">
                    <div class="header__status-container">
                        <div class="status__indicator" id="statusIndicator"></div>
                        <span class="status__text" id="statusText">Checking page...</span>
                    </div>
                    <div class="header__actions">
                        <button class="btn btn--orange" id="proListReviewScraperBtn" style="display: none;"> Data Extractors</button>
                        <button class="btn--mini" id="extractAttributesBtn" style="display: none;">Extract Attributes</button>
                        <span class="claim-status" id="claimStatus" style="display: none;"></span>
                    </div>
                </div>
                
                <!-- Version Indicator (shown when main status is hidden) -->
                <div class="header__status-wrapper" id="versionWrapper" style="display: none;">
                    <div class="header__status-container">
                        <div class="status__indicator status__indicator--active" id="versionIndicator"></div>
                        <span class="status__text" id="versionText">Loading version...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Quick Timer Section -->
        <div class="quick-timer-section" id="quickTimerSection" style="display: none;">
            <div class="quick-timer-header" id="quickTimerHeader">
                <div class="quick-timer-title">
                    <span class="quick-timer-status-indicator" id="quickTimerStatusIndicator"></span>
                    <span id="quickTimerTitle">Quick Timer</span>
                </div>
                <span class="quick-timer-icon" id="quickTimerIcon">▼</span>
            </div>
            
            <div class="quick-timer-content" id="quickTimerContent">
                <div class="timer-controls-form">
                    <div class="timer-presets">
                        <button class="timer-preset timer-preset-custom" data-custom="true">CUSTOM</button>
                        <button class="timer-preset" data-minutes="0.167">
                            <span class="timer-preset-text">10 sec</span>
                            <span class="timer-preset-plus" data-minutes="0.167">+</span>
                        </button>
                        <button class="timer-preset" data-minutes="0.5">
                            <span class="timer-preset-text">30 sec</span>
                            <span class="timer-preset-plus" data-minutes="0.5">+</span>
                        </button>
                        <button class="timer-preset" data-minutes="1">
                            <span class="timer-preset-text">1 min</span>
                            <span class="timer-preset-plus" data-minutes="1">+</span>
                        </button>
                        <button class="timer-preset" data-minutes="5">
                            <span class="timer-preset-text">5 min</span>
                            <span class="timer-preset-plus" data-minutes="5">+</span>
                        </button>
                        <button class="timer-preset" data-minutes="10">
                            <span class="timer-preset-text">10 min</span>
                            <span class="timer-preset-plus" data-minutes="10">+</span>
                        </button>
                        <button class="timer-preset" data-minutes="30">
                            <span class="timer-preset-text">30 min</span>
                            <span class="timer-preset-plus" data-minutes="30">+</span>
                        </button>
                        <button class="timer-preset" data-minutes="60">
                            <span class="timer-preset-text">1 hour</span>
                            <span class="timer-preset-plus" data-minutes="60">+</span>
                        </button>
                    </div>
                    
                    <!-- Custom Duration Input -->
                    <div class="timer-custom-input" id="timerCustomInput" style="display: none;">
                        <input type="text" id="customDurationInput" class="custom-duration-input" placeholder="e.g., 1min, 30sec, 2.5hrs" maxlength="20">
                        <button class="timer-custom-set-btn" id="timerCustomSetBtn">Set</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Independent Tasks Section -->
        <div class="tasks-section" id="tasksSection" style="display: none;">
            <!-- Compact Toggle Button (shown when header is hidden) -->
            <div class="tasks-compact-toggle" id="tasksCompactToggle">
                <span class="tasks-compact-icon" id="tasksCompactIcon">▼</span>
            </div>
            
            <div class="tasks-header" id="tasksHeader">
                <div class="tasks-title">
                    <span class="tasks-status-indicator" id="tasksStatusIndicator"></span>
                    <span id="tasksTitle">Tasks</span>
                    <span id="tasksCount" style="color: #7C3AED; font-weight: 600;"></span>
                </div>
                <span class="tasks-icon" id="tasksIcon">▼</span>
            </div>
            
            <div class="tasks-active-tasks" id="tasksActiveTasks">
                <!-- Active tasks displayed here -->
            </div>
            
            <div class="tasks-content" id="tasksContent" style="display: none;">
                <div class="tasks-add-form">
                    <input type="text" id="tasksNewTaskInput" class="tasks-new-task-input" placeholder="Add new task..." maxlength="100">
                    <button id="tasksAddTaskBtn" class="tasks-add-task-btn">Add Task</button>
                </div>
                
                <div class="tasks-list" id="tasksList">
                    <!-- All tasks listed here -->
                </div>
                
            </div>
        </div>

        <!-- Independent Pomodoro Timer Section -->
        <div class="pomodoro-section" id="pomodoroSection" style="display: none;">
            <div class="pomodoro-header" id="pomodoroTimerHeader">
                <div class="pomodoro-title">
                    <span class="pomodoro-status-indicator" id="pomodoroStatusIndicator"></span>
                    <span id="pomodoroTimerTitle">Pomodoro Timer</span>
                    <span id="pomodoroCycleDisplay" style="color: #fff; font-weight: 600; font-size: 12px;"></span>
                    <span id="pomodoroTimeRemaining" style="color:#fff; font-weight: 600;"></span>
                </div>
                <div class="pomodoro-header-right">
                    <span class="pomodoro-icon" id="pomodoroTimerIcon">▼</span>
                </div>
            </div>
            
            <div class="pomodoro-controls" id="pomodoroControls">
                <div class="pomodoro-header-controls">
                    <!-- Mode selection moved to header -->
                </div>
            </div>
            
            <div class="pomodoro-content" id="pomodoroTimerContent" style="display: none;">

                <!-- Timer Configuration Accordion -->
                <div class="pomodoro-accordion">
                    <div class="pomodoro-header" id="pomodoroConfigHeader">
                        <div class="pomodoro-title">
                            <span style="color: #7C3AED; font-size: 16px;">●</span> Timer Configuration
                        </div>
                        <span class="pomodoro-icon" id="pomodoroConfigIcon">▼</span>
                    </div>
                    <div class="pomodoro-content" id="pomodoroConfigContent">
                        <div class="pomodoro-body">
                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Work Duration (minutes):</label>
                                <input type="number" id="pomodoroWorkDuration" class="pomodoro-input" data-setting="pomodoroWorkDuration" min="5" max="180" value="25">
                            </div>
                            
                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Short Break (minutes):</label>
                                <input type="number" id="pomodoroShortBreak" class="pomodoro-input" data-setting="pomodoroShortBreak" min="1" max="60" value="5">
                            </div>
                            
                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Long Break (minutes):</label>
                                <input type="number" id="pomodoroLongBreak" class="pomodoro-input" data-setting="pomodoroLongBreak" min="1" max="120" value="15">
                            </div>
                            
                            
                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Number of Cycles:</label>
                                <select id="pomodoroNumberOfCycles" class="pomodoro-select" data-setting="pomodoroNumberOfCycles">
                                    <option value="1">1 cycle</option>
                                    <option value="2">2 cycles</option>
                                    <option value="3">3 cycles</option>
                                    <option value="4">4 cycles</option>
                                    <option value="5">5 cycles</option>
                                    <option value="6">6 cycles</option>
                                    <option value="7">7 cycles</option>
                                    <option value="8" selected>8 cycles</option>
                                    <option value="9">9 cycles</option>
                                    <option value="10">10 cycles</option>
                                    <option value="11">11 cycles</option>
                                    <option value="12">12 cycles</option>
                                    <option value="unlimited">Unlimited</option>
                                </select>
                            </div>
                            
                            <div class="pomodoro-input-group" style="margin-top: 15px;">
                                <button id="pomodoroResetDefaults" class="pomodoro-reset-btn">
                                    Reset to Defaults (25/5/15/60)
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audio Settings Accordion -->
                <div class="pomodoro-accordion">
                    <div class="pomodoro-header" id="pomodoroAudioHeader">
                        <div class="pomodoro-title">
                            <span style="color: #7C3AED; font-size: 16px;">●</span> Audio Settings
                        </div>
                        <span class="pomodoro-icon" id="pomodoroAudioIcon">▼</span>
                    </div>
                    <div class="pomodoro-content" id="pomodoroAudioContent">
                        <div class="pomodoro-body">
                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Work Completed Sound:</label>
                                <div class="pomodoro-sound-selector">
                                    <select id="pomodoroWorkCompletedSound" class="pomodoro-select" data-setting="pomodoroWorkCompletedSound">
                                        <option value="Bell Meditation">Bell Meditation</option>
                                        <option value="Celestial Gong">Celestial Gong</option>
                                        <option value="Deep Meditation Bell Crown Chakra">Deep Meditation Bell Crown Chakra</option>
                                        <option value="Deep Meditation Bell Heart Chakra">Deep Meditation Bell Heart Chakra</option>
                                        <option value="Funky">Funky</option>
                                        <option value="Mechanical">Mechanical</option>
                                        <option value="Notification 1">Notification 1</option>
                                        <option value="Notification 2">Notification 2</option>
                                        <option value="Notification 3">Notification 3</option>
                                        <option value="Notification 4">Notification 4</option>
                                        <option value="Old Church Bell 2">Old Church Bell 2</option>
                                        <option value="Old Church Bell 3">Old Church Bell 3</option>
                                    </select>
                                    <button id="pomodoroWorkSoundPreview" class="pomodoro-preview-btn" type="button" title="Preview sound">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8 5v14l11-7z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">End Break Sound:</label>
                                <div class="pomodoro-sound-selector">
                                    <select id="pomodoroEndBreakSound" class="pomodoro-select" data-setting="pomodoroEndBreakSound">
                                        <option value="Bell Meditation">Bell Meditation</option>
                                        <option value="Celestial Gong">Celestial Gong</option>
                                        <option value="Deep Meditation Bell Crown Chakra">Deep Meditation Bell Crown Chakra</option>
                                        <option value="Deep Meditation Bell Heart Chakra">Deep Meditation Bell Heart Chakra</option>
                                        <option value="Funky">Funky</option>
                                        <option value="Mechanical">Mechanical</option>
                                        <option value="Notification 1">Notification 1</option>
                                        <option value="Notification 2">Notification 2</option>
                                        <option value="Notification 3">Notification 3</option>
                                        <option value="Notification 4">Notification 4</option>
                                        <option value="Old Church Bell 2">Old Church Bell 2</option>
                                        <option value="Old Church Bell 3">Old Church Bell 3</option>
                                    </select>
                                    <button id="pomodoroBreakSoundPreview" class="pomodoro-preview-btn" type="button" title="Preview sound">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8 5v14l11-7z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>


                            <div class="pomodoro-checkbox-group">
                                <input type="checkbox" id="pomodoroCompletionNotifications" class="pomodoro-checkbox" data-setting="pomodoroCompletionNotifications" checked>
                                <label for="pomodoroCompletionNotifications" class="pomodoro-checkbox-label">Completion notifications</label>
                            </div>


                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Notifications Sound Level:</label>
                                <input type="range" id="pomodoroNotificationVolume" class="pomodoro-slider" data-setting="pomodoroNotificationVolume" min="0" max="100" value="70">
                                <span class="pomodoro-slider-value" id="pomodoroNotificationVolumeValue">70%</span>
                            </div>

                            <div class="pomodoro-checkbox-group">
                                <input type="checkbox" id="pomodoroChronometerSound" class="pomodoro-checkbox" data-setting="pomodoroChronometerSound" checked>
                                <label for="pomodoroChronometerSound" class="pomodoro-checkbox-label">Chronometer sound</label>
                                <div class="chronometer-info-icon" data-tooltip="The ticking sound helps users with neurodivergent brains and time blindness stay aware of how much time is passing and manage tasks more effectively."></div>
                            </div>

                            <div class="pomodoro-checkbox-group">
                                <input type="checkbox" id="pomodoroChronometerOnBreak" class="pomodoro-checkbox" data-setting="pomodoroChronometerOnBreak">
                                <label for="pomodoroChronometerOnBreak" class="pomodoro-checkbox-label">Chronometer on break</label>
                            </div>

                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Chronometer Frequency:</label>
                                <div class="pomodoro-frequency-group">
                                    <span>tick per</span>
                                    <input type="number" id="pomodoroChronometerFrequency" class="pomodoro-frequency-input" data-setting="pomodoroChronometerFrequency" min="1" max="10" value="2">
                                    <span>seconds</span>
                                </div>
                            </div>

                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Ticking Sound:</label>
                                <div class="pomodoro-sound-selector">
                                    <select id="pomodoroTickingSound" class="pomodoro-select" data-setting="pomodoroTickingSound">
                                        <option value="Clock Ticking 1">Clock Ticking 1</option>
                                        <option value="Clock Ticking 2">Clock Ticking 2</option>
                                        <option value="Clock Ticking 3">Clock Ticking 3</option>
                                    </select>
                                    <button id="pomodoroTickingSoundPreview" class="pomodoro-preview-btn" type="button" title="Preview ticking sound (3 ticks)">
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8 5v14l11-7z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Chronometer Sound Level:</label>
                                <input type="range" id="pomodoroChronometerVolume" class="pomodoro-slider" data-setting="pomodoroChronometerVolume" min="0" max="100" value="30">
                                <span class="pomodoro-slider-value" id="pomodoroChronometerVolumeValue">30%</span>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Website Blocking Accordion -->
                <div class="pomodoro-accordion">
                    <div class="pomodoro-header" id="pomodoroBlockingHeader">
                        <div class="pomodoro-title">
                            <span style="color: #7C3AED; font-size: 16px;">●</span> Website Blocking
                        </div>
                        <span class="pomodoro-icon" id="pomodoroBlockingIcon">▼</span>
                    </div>
                    <div class="pomodoro-content" id="pomodoroBlockingContent">
                        <div class="pomodoro-body">
                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Blocked Sites (one per line):</label>
                                <textarea id="pomodoroBlockedSites" class="pomodoro-textarea" data-setting="pomodoroBlockedSites" rows="6" placeholder="facebook.com&#10;twitter.com&#10;instagram.com&#10;youtube.com&#10;reddit.com&#10;tiktok.com">facebook.com
twitter.com
instagram.com
youtube.com
reddit.com
tiktok.com</textarea>
                            </div>
                            
                            <div class="pomodoro-input-group">
                                <label class="pomodoro-input-label">Whitelisted Sites (one per line):</label>
                                <textarea id="pomodoroWhitelistedSites" class="pomodoro-textarea" data-setting="pomodoroWhitelistedSites" rows="4" placeholder="google.com&#10;maps.google.com&#10;github.com">google.com
maps.google.com
github.com</textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="pomodoro-todo-integration" id="pomodoroTodoIntegration" style="display: none;">
                    <h4>Active Tasks During Session:</h4>
                    <div class="pomodoro-active-tasks" id="pomodoroActiveTasks">
                        <!-- Tasks from Tasks section can be shown here when both are enabled -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Location Changer Accordion -->
        <div class="location-changer-accordion">
            <div class="location-changer-header" id="locationChangerHeader">
                <div class="location-changer-title">
                    <span class="location-status-indicator" id="locationStatusIndicator"></span>
                    Location Changer
                </div>
                <div class="current-location-display" id="currentLocationDisplay">
                    <span class="current-location-text" id="currentLocationText">No location set</span>
                </div>
                <span class="location-changer-icon" id="locationChangerIcon">▼</span>
            </div>
            <div class="location-changer-content" id="locationChangerContent">
                <div class="location-changer-body">
                    <div class="location-input-group">
                        <label class="location-input-label">Location</label>
                        <input id="place" type="text" class="location-input" placeholder="Google Building 40, Amphitheatre Parkway, Mountain View, CA, USA">
                    </div>

                    <div class="location-input-group">
                        <div class="location-input-row">
                            <div class="location-input-wrapper">
                                <label class="location-input-label">Host Language</label>
                                <input type="text" class="location-input" id="hl" placeholder="en">
                            </div>
                            <div class="location-input-wrapper">
                                <label class="location-input-label">Geo Location</label>
                                <input type="text" class="location-input" id="gl" placeholder="US">
                            </div>
                        </div>
                        <input id="regions" class="typeahead location-input" autocomplete="off" type="text" placeholder="select language & location" />
                    </div>

                    <div class="location-input-group">
                        <div class="location-input-row">
                            <div class="location-input-wrapper">
                                <label class="location-input-label">Latitude</label>
                                <input type="text" class="location-input" id="latitude" placeholder="37.422388">
                            </div>
                            <div class="location-input-wrapper">
                                <label class="location-input-label">Longitude</label>
                                <input type="text" class="location-input" id="longitude" placeholder="-122.0841883">
                            </div>
                        </div>
                        <div class="location-checkbox-wrapper">
                            <input type="checkbox" class="location-checkbox" id="enabled">
                            <label for="enabled" class="location-checkbox-label">Enable location overwrite</label>
                            <button class="check-location-btn" id="checkLocationBtn" title="Check your current location">
                                <span style="color: #7C3AED; font-size: 14px;">●</span> Check Location
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Quick Actions Section - Shown on non-Google pages -->
        <div class="controls controls--compact" id="quickActionsControls" style="display: none;">
            <div class="quick-actions-layout">
                <div class="quick-actions-left">
                    <div class="quick-actions-header">
                        <h3 class="quick-actions-title">⚡ Quick Actions</h3>
                    </div>
                    <div class="quick-actions-buttons">
                        <button class="btn btn--secondary" id="htagsBtn" style="display: none;">Htags</button>
                        <button class="btn btn--secondary" id="headingStructureBtn" style="display: none;">Heading Structure</button>
                        <button class="btn btn--secondary" id="showLinksBtn" style="display: none;">Show Links</button>
                        <button class="btn btn--secondary" id="showHiddenBtn" style="display: none;">Show Hidden</button>
                        <!-- Keyword functionality moved to right-click context menu -->
                        <button class="btn btn--secondary" id="boldFromSerpBtn" style="display: none;">Bold From SERP</button>
                        <button class="btn btn--secondary" id="schemaBtn" style="display: none;">Schema</button>
                        <button class="btn btn--secondary" id="imagesBtn" style="display: none;">Images</button>
                        <button class="btn btn--secondary" id="metadataBtn" style="display: none;">Metadata</button>
                        <button class="btn btn--secondary" id="utmBuilderBtn" style="display: none;">UTM Builder</button>
                        <button class="btn btn--secondary" id="pageStructureBtn" style="display: none;">Page Structure</button>
                        <button class="btn btn--secondary" id="copyElementBtn" style="display: none;">Copy Element</button>
                        <button class="btn btn--secondary" id="linksExtractorBtn" style="display: none;">Links Extractor</button>
                        <button class="btn btn--secondary" id="bulkLinkOpenBtn" style="display: none;">Bulk Link Open</button>
                        <button class="btn btn--secondary" id="colorPickerBtn" style="display: none;">Color Picker</button>
                        <button class="btn btn--secondary" id="responsiveBtn" style="display: none;">Responsive</button>
                        <button class="btn btn--secondary" id="seoTestsBtn" style="display: none;">SEO Tests</button>
                        <button class="btn btn--secondary" id="trackerDetectionBtn" style="display: none;">Tracker Detection</button>
                    </div>
                </div>
                <div class="quick-actions-right">
                    <div class="youtube-scraper-container" id="youtubeScraperContainer" style="display: none;">
                        <div class="youtube-scraper-icon" id="youtubeScraperIcon" title="Navigate to your YouTube channel's main videos page to scrape and copy video embed codes automatically.">
                            <svg width="32" height="32" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="20" fill="#FF0000"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M35.3005 16.3781C35.6996 16.7772 35.9872 17.2739 36.1346 17.8187C36.9835 21.2357 36.7873 26.6324 36.1511 30.1813C36.0037 30.7261 35.7161 31.2228 35.317 31.6219C34.9179 32.021 34.4212 32.3086 33.8764 32.456C31.8819 33 23.8544 33 23.8544 33C23.8544 33 15.8269 33 13.8324 32.456C13.2876 32.3086 12.7909 32.021 12.3918 31.6219C11.9927 31.2228 11.7051 30.7261 11.5577 30.1813C10.7038 26.7791 10.9379 21.3791 11.5412 17.8352C11.6886 17.2903 11.9762 16.7936 12.3753 16.3945C12.7744 15.9954 13.2711 15.7079 13.8159 15.5604C15.8104 15.0165 23.8379 15 23.8379 15C23.8379 15 31.8654 15 33.8599 15.544C34.4047 15.6914 34.9014 15.979 35.3005 16.3781ZM27.9423 24L21.283 27.8571V20.1428L27.9423 24Z" fill="white"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profiles Section -->
        <div class="controls controls--compact" id="profilesControls" style="display: none;">
            <div class="profiles-layout">
                <div class="profiles-header">
                    <h3 class="profiles-title">✨ Profiles</h3>
                </div>
                <div class="profiles-buttons quick-actions-buttons" id="profiles-buttons-container">
                    <!-- Profile buttons will be injected here -->
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn--secondary" id="exportBtn" disabled>Export CSV</button>
            <button class="btn btn--secondary" id="copyTextBtn" disabled>Copy Text</button>
        </div>

        <div class="controls">
            <button class="btn btn--secondary" id="stopReviewAnalysisBtn" disabled style="display: none;">Stop Analysis</button>
        </div>

        <div class="controls">
            <button class="btn btn--orange" id="singleReviewScraperBtn" style="display: none;">⚡ Data Extractor</button>
            <button class="btn btn--secondary" id="exportSingleReviewBtn" disabled style="display: none;">Export Reviews CSV</button>
        </div>

        <div class="controls" id="servicesExtractionControls" style="display: none;">
            <button class="btn btn--orange" id="servicesExtractionBtn">Open Extractor Module</button>
            <button class="btn btn--secondary" id="exportReviewBtn" disabled style="display: none;">Export Review Data</button>
            <button class="btn btn--secondary" id="exportServicesBtn" disabled>Export Services CSV</button>
        </div>

        <div class="loading" id="loading">
            <div class="loading__spinner"></div>
            <p>Extracting GMB data...</p>
        </div>

        <main class="data-container" id="dataContainer" style="display: none;">
            <div class="empty-state">
            </div>
        </main>

        <section class="data-container" id="reviewAnalysisContainer" style="display: none;">
            <div class="data-section__title">Review Analysis Progress</div>
            <div id="reviewProgress" class="data-item">
                <div class="data-item__label">Status:</div>
                <div class="data-item__value" id="reviewStatus">Ready</div>
            </div>
            <div id="reviewProgressBar" class="data-item" style="display: none;">
                <div class="data-item__label">Progress:</div>
                <div class="data-item__value">
                    <span id="reviewProgressText">0/0</span>
                    <div class="progress-bar">
                        <div id="reviewProgressFill" class="progress-bar__fill"></div>
                    </div>
                </div>
            </div>
            <div id="reviewResults" class="data-section" style="display: none;">
                <div class="data-section__title">Review Analysis Results</div>
                <div id="reviewResultsList"></div>
            </div>
        </section>

        <section class="data-container" id="singleReviewContainer" style="display: none;">
            <div id="singleReviewResults" class="data-section">
                <div class="data-section__title">Data Extractor Results</div>
                <div id="singleReviewProgress" class="data-item" style="display: none;">
                    <div class="data-item__label">Progress</div>
                    <div class="data-item__value" id="singleReviewProgressText">Starting...</div>
                </div>
                <div id="singleReviewResultsList"></div>
            </div>
        </section>

        <section class="data-container" id="servicesExtractionContainer" style="display: none;">
            <div class="data-section__title">Services Extraction Progress</div>
            <div id="servicesProgress" class="data-item">
                <div class="data-item__label">Status:</div>
                <div class="data-item__value" id="servicesStatus">Ready</div>
            </div>
            <div id="servicesProgressBar" class="data-item" style="display: none;">
                <div class="data-item__label">Progress:</div>
                <div class="data-item__value">
                    <span id="servicesProgressText">0/0</span>
                    <div class="progress-bar">
                        <div id="servicesProgressFill" class="progress-bar__fill"></div>
                    </div>
                </div>
            </div>
            <div id="servicesResults" class="data-section" style="display: none;">
                <div class="data-section__title">Services Extraction Results</div>
                <div id="servicesResultsList"></div>
            </div>
        </section>
    </div>

    <!-- Location Changer Dependencies -->
    <script type="text/javascript" src="js/location-changer-js/jquery-3.7.1.min.js"></script>
    <script type="text/javascript" src="js/location-changer-js/handlebars.runtime.min-v4.7.7.js"></script>
    <script type="text/javascript" src="js/location-changer-js/typeahead.precompiled.js"></script>
    <script type="text/javascript" src="js/location-changer-js/locations.precompiled.js"></script>
    <script type="text/javascript" src="js/location-changer-js/typeahead.bundle.min.0.11.1.js"></script>
    
    <!-- Existing Scripts -->
    <script src="js/review-range-utility.js"></script>
    <script src="js/storage-protection.js"></script>
    <script src="js/logout-debug-monitor.js"></script>
    <script src="settings/logging-utility.js"></script>
    <script src="settings/notification-utility.js"></script>
    <script src="settings/extension-reload-utility.js"></script>
    
    <!-- Location Changer Logic -->
    <script src="js/location-changer-js/popup.js"></script>
    
    <!-- Location Changer Integration -->
    <script src="js/location-changer-integration.js"></script>
    
    <!-- Location Changer Visibility Manager -->
    <script src="js/location-changer-visibility.js"></script>
    
    <!-- Location Changer Favorites -->
    <script src="js/location-changer-js/favorites.js"></script>

    <!-- Profiles Frontend -->
    <script src="js/profiles-frontend.js"></script>
    <script src="js/profiles-shortcuts.js"></script>
    
    <!-- Quick Actions Scripts -->
    <script src="settings/quick-actions/htags.js"></script>
    <script src="settings/quick-actions/headingstructure.js"></script>
    <script src="settings/quick-actions/showlinks.js"></script>
    <script src="settings/quick-actions/showhidden.js"></script>
    <script src="settings/quick-actions/keyword.js"></script>
    <script src="settings/quick-actions/boldfromserp.js"></script>
    <script src="settings/quick-actions/youtubeembedscraper.js"></script>
    <script src="settings/quick-actions/schema.js"></script>
    <script src="settings/quick-actions/images.js"></script>
    <script src="settings/quick-actions/metadata.js"></script>
    <script src="settings/quick-actions/utmbuilder.js"></script>
    <script src="settings/quick-actions/pagestructure.js"></script>
    <script src="settings/quick-actions/image-download.js"></script>
    <script src="settings/quick-actions/copyelement.js"></script>
    <script src="settings/quick-actions/linksextractor.js"></script>
    <script src="settings/quick-actions/bulklinkopen.js"></script>
    <script src="settings/quick-actions/colorpicker.js"></script>
    <script src="settings/quick-actions/responsive.js"></script>
    <script src="settings/quick-actions/seotests.js"></script>
    <script src="settings/quick-actions/trackerdetection.js"></script>
    <script src="settings/quick-actions/quick-actions-shortcuts.js"></script>
    
    <!-- Pomodoro Scripts -->
    <script src="js/pomodoro/todo-manager.js"></script>
    <script src="js/pomodoro/sound-preview.js"></script>
    <script src="js/pomodoro/pomodoro-popup.js"></script>
    
    
    <!-- Tooltip System -->
    <script src="js/tooltip-system.js"></script>
    <script src="js/info-icon-initializer.js"></script>
    
    
    <!-- Popup Shortcuts -->
    <script src="js/popup-shortcuts.js"></script>
    
    <!-- Main Popup Script -->
    <script src="js/popup.js"></script>
    <script src="js/quick-timer.js"></script>
</body>
</html> 