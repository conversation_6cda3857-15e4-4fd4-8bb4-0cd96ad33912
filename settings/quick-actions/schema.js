// Schema Action - Extract structured data (JSON-LD) from web pages
class SchemaAction {
    static execute() {
        try {
            // Inject the Schema extraction script into the active tab
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        // The main schema extraction function to inject
                        (function() {
                            var structuredData = document.querySelectorAll('script[type="application/ld+json"]');
                            
                            if (structuredData.length === 0) {
                                alert("No structured data (JSON-LD) found on this page.");
                                return;
                            }
                            
                            var jsonData = [];
                            structuredData.forEach(function(script) {
                                try {
                                    var data = JSON.parse(script.textContent);
                                    jsonData.push(data);
                                } catch (error) {
                                    console.error("Error parsing JSON-LD:", error);
                                }
                            });
                            
                            if (jsonData.length === 0) {
                                alert("No valid structured data (JSON-LD) found on this page.");
                                return;
                            }
                            
                            var info = JSON.stringify(jsonData, null, 2);
                            
                            // Create modal with dark theme styling
                            var modal = document.createElement('div');
                            modal.className = 'schema-modal';
                            modal.style.position = 'fixed';
                            modal.style.top = '50%';
                            modal.style.left = '50%';
                            modal.style.transform = 'translate(-50%, -50%)';
                            modal.style.padding = '20px';
                            modal.style.backgroundColor = '#0f0f0f';
                            modal.style.color = '#e5e5e5';
                            modal.style.border = '1px solid #2a2a2a';
                            modal.style.borderRadius = '12px';
                            modal.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.4)';
                            modal.style.zIndex = '9999999';
                            modal.style.width = '80%';
                            modal.style.height = '80%';
                            modal.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif';
                            
                            // Create header
                            var header = document.createElement('h2');
                            header.textContent = 'Structured Data (JSON-LD) - Schema Extractor';
                            header.style.margin = '0 0 16px 0';
                            header.style.color = '#ffffff';
                            header.style.fontSize = '18px';
                            header.style.fontWeight = '700';
                            header.style.letterSpacing = '-0.5px';
                            modal.appendChild(header);
                            
                            // Create textarea
                            var textArea = document.createElement("textarea");
                            textArea.value = info;
                            textArea.style.width = '100%';
                            textArea.style.height = 'calc(100% - 80px)';
                            textArea.style.resize = 'none';
                            textArea.style.backgroundColor = '#1a1a1a';
                            textArea.style.color = '#e5e5e5';
                            textArea.style.border = '1px solid #333333';
                            textArea.style.borderRadius = '8px';
                            textArea.style.padding = '12px';
                            textArea.style.fontSize = '13px';
                            textArea.style.fontFamily = 'monospace';
                            textArea.style.lineHeight = '1.4';
                            modal.appendChild(textArea);
                            
                            // Create button container
                            var buttonContainer = document.createElement('div');
                            buttonContainer.style.display = 'flex';
                            buttonContainer.style.gap = '8px';
                            buttonContainer.style.marginTop = '12px';
                            buttonContainer.style.justifyContent = 'flex-end';
                            
                            // Create copy button
                            var copyButton = document.createElement("button");
                            copyButton.textContent = "Copy to Clipboard";
                            copyButton.style.padding = '10px 16px';
                            copyButton.style.background = 'linear-gradient(135deg, #7c3aed, #a855f7)';
                            copyButton.style.color = '#ffffff';
                            copyButton.style.border = '1px solid #8b5cf6';
                            copyButton.style.borderRadius = '8px';
                            copyButton.style.cursor = 'pointer';
                            copyButton.style.fontSize = '13px';
                            copyButton.style.fontWeight = '600';
                            copyButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                            copyButton.onclick = function() {
                                textArea.select();
                                document.execCommand("copy");
                                
                                // Visual feedback
                                var originalText = copyButton.textContent;
                                copyButton.textContent = "Copied!";
                                copyButton.style.background = 'linear-gradient(135deg, #22c55e, #16a34a)';
                                copyButton.style.borderColor = '#16a34a';
                                
                                setTimeout(function() {
                                    copyButton.textContent = originalText;
                                    copyButton.style.background = 'linear-gradient(135deg, #7c3aed, #a855f7)';
                                    copyButton.style.borderColor = '#8b5cf6';
                                }, 2000);
                            };
                            buttonContainer.appendChild(copyButton);
                            
                            // Create close button
                            var closeButton = document.createElement("button");
                            closeButton.textContent = "✕";
                            closeButton.style.padding = '10px 16px';
                            closeButton.style.backgroundColor = '#262626';
                            closeButton.style.color = '#ffffff';
                            closeButton.style.border = '1px solid #333333';
                            closeButton.style.borderRadius = '8px';
                            closeButton.style.cursor = 'pointer';
                            closeButton.style.fontSize = '13px';
                            closeButton.style.fontWeight = '600';
                            closeButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                            closeButton.onclick = function() {
                                document.body.removeChild(modal);
                            };
                            buttonContainer.appendChild(closeButton);
                            
                            modal.appendChild(buttonContainer);
                            
                            // Add hover effects
                            copyButton.addEventListener('mouseenter', function() {
                                if (this.textContent !== "Copied!") {
                                    this.style.transform = 'translateY(-1px)';
                                    this.style.boxShadow = '0 8px 24px rgba(139, 92, 246, 0.3)';
                                }
                            });
                            
                            copyButton.addEventListener('mouseleave', function() {
                                this.style.transform = 'translateY(0)';
                                this.style.boxShadow = 'none';
                            });
                            
                            closeButton.addEventListener('mouseenter', function() {
                                this.style.backgroundColor = '#333333';
                                this.style.transform = 'translateY(-1px)';
                                this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.3)';
                            });
                            
                            closeButton.addEventListener('mouseleave', function() {
                                this.style.backgroundColor = '#262626';
                                this.style.transform = 'translateY(0)';
                                this.style.boxShadow = 'none';
                            });
                            
                            // Handle Escape key to close modal
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    document.body.removeChild(modal);
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            }
                            document.addEventListener('keydown', handleKeyDown);
                            
                            document.body.appendChild(modal);
                        })();
                    }
                }, (result) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error executing Schema script:', chrome.runtime.lastError);
                    } else {
                        console.log('Schema script executed successfully');
                    }
                });
            });
        } catch (error) {
            console.error('Schema error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                // Function to reset Schema effects
                function resetSchema() {
                    // Remove any existing schema modal
                    const existingModal = document.querySelector('.schema-modal');
                    if (existingModal) {
                        existingModal.remove();
                        console.log('Schema modal removed');
                    }
                    
                    console.log('Schema reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetSchema
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('Schema reset error:', error);
                resolve(); // Resolve anyway to not block other resets
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SchemaAction;
} else {
    window.SchemaAction = SchemaAction;
} 