<div class="settings-section">
    <div class="settings-group">
        <h3 class="settings-group__title">Quick Actions</h3>
        <p class="settings-group__description">Quick action tools that appear on non-Google pages below the search inputs.</p>
        
        <!-- Search Bar for Quick Actions -->
        <div class="quick-actions-search-container">
            <input type="text" id="quickActionsSearch" class="quick-actions-search" placeholder="Search Quick Actions... (3+ letters)" autocomplete="off">
            <button type="button" id="clearQuickActionsSearch" class="quick-actions-search-clear" title="Clear search">×</button>
        </div>
        
        <!-- Select All/Deselect All Toggle -->
        <div class="settings-item settings-item--select-all">
            <div class="settings-item__content">
                <div class="settings-item__info">
                    <div class="settings-item__label">Select All / Deselect All</div>
                    <div class="settings-item__description">Toggle all Quick Actions on or off</div>
                </div>
                <div class="settings-item__control">
                    <div class="toggle-switch toggle-switch--active" id="quickActionsSelectAll" data-accordion="quickActions"></div>
                </div>
            </div>
        </div>
        
        <!-- Keyboard Shortcuts Settings -->
        <div class="settings-item">
            <div class="settings-item__content">
                <div class="settings-item__info">
                    <h4 class="settings-item__title">Keyboard Shortcuts</h4>
                    <p class="settings-item__description">Enable <code style="background: #2a2a2a; padding: 2px 6px; border-radius: 4px; font-family: monospace; color: #d1d5db;">Ctrl+Shift+1-9</code> shortcuts to trigger Quick Actions. Numbers appear on buttons when enabled.</p>
                </div>
                <div class="settings-item__control">
                    <div class="toggle-switch toggle-switch--active" data-setting="keyboardShortcutsEnabled">
                        <div class="toggle-switch__track">
                            <div class="toggle-switch__thumb"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Htags Highlighter</h4>
                <p class="settings-item__description">Highlights and color-codes all heading tags (H1-H6) on the current page with different colors.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="htagsEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Heading Structure</h4>
                <p class="settings-item__description">Analyzes and displays the page's heading structure (H1-H6) with statistics, visual hierarchy, and interactive navigation in a new window.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="headingStructureEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Show Links</h4>
                <p class="settings-item__description">Highlights all links on the page with different colors based on their rel attributes (nofollow, ugc, sponsored, or regular links).</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="showLinksEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Show Hidden</h4>
                <p class="settings-item__description">Toggles the visibility of hidden elements on the page (display:none, aria-hidden). Useful for debugging and content analysis.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="showHiddenEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Keyword Density (Right-Click Menu)</h4>
                <p class="settings-item__description">Available as a right-click context menu item. Select text on any page and right-click to calculate keyword density. Shows occurrences, total words, and density percentage.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="keywordEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Bold From SERP</h4>
                <p class="settings-item__description">Extracts unique n-grams from all emphasized text (&lt;em&gt; tags) on the page and copies them to clipboard for SEO analysis.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="boldFromSerpEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Schema Extractor</h4>
                <p class="settings-item__description">Extracts and displays all structured data (JSON-LD) found on the current page in a formatted modal with copy-to-clipboard functionality.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="schemaEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Images SEO Audit</h4>
                <p class="settings-item__description">Comprehensive image audit tool that analyzes all images on the page for SEO issues including missing alt text, broken images, file formats, dimensions, and optimization opportunities.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="imagesEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Metadata Analyzer</h4>
                <p class="settings-item__description">Comprehensive metadata analysis tool that examines page title, meta description, robots tags, canonical URLs, Open Graph, Twitter Cards, and all other meta tags with SEO recommendations and social media previews.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="metadataEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">UTM Builder</h4>
                <p class="settings-item__description">Create UTM tracking URLs with campaign parameters for Google Analytics. Features template management for saving and reusing parameter combinations, real-time URL preview, and easy copy-to-clipboard functionality.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="utmBuilderEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Page Structure</h4>
                <p class="settings-item__description">Analyzes page structure and displays DOM hierarchy, element counts, and interactive element picker with visual outlines and hover effects.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="pageStructureEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Copy Element</h4>
                <p class="settings-item__description">Interactive element copying tool. Hover over any element to highlight it, then click to copy its content. Click normally copies text, click images copies URL, Shift+click copies full HTML.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="copyElementEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="custom-shortcut-container">
            <div class="custom-shortcut-label">
                <span style="color: #7C3AED; font-size: 18px;">●</span>
                Custom Global Shortcut
            </div>
            <div class="custom-shortcut-input-wrapper">
                <input 
                    type="text" 
                    id="copyElementCustomShortcut" 
                    class="custom-shortcut-input" 
                    data-setting="copyElementShortcut"
                    placeholder="Type shortcut (e.g., Ctrl+Shift+C)"
                    title="Press the keys you want to use as a shortcut. Examples: Ctrl+Shift+C, Alt+E, Ctrl+Cmd+X"
                />
                <div class="shortcut-help">
                    <span class="help-icon">ℹ️</span>
                    <div class="help-tooltip">
                        <strong>Supported formats:</strong><br>
                        • Ctrl+Shift+C<br>
                        • Alt+E<br>
                        • Ctrl+Cmd+X<br>
                        • Meta+Shift+Z<br><br>
                        <strong>Modifiers:</strong> Ctrl, Alt, Shift, Meta/Cmd<br>
                        <strong>Keys:</strong> A-Z, 0-9
                    </div>
                </div>
            </div>
            <div class="shortcut-status" id="shortcutStatus"></div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Links Extractor</h4>
                <p class="settings-item__description">Interactive tool to extract and analyze links from selected page elements. Hover over elements to highlight them, click to extract all links within that element with detailed analysis including link types, domains, and export options.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="linksExtractorEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Bulk Link Open</h4>
                <p class="settings-item__description">Paste, edit, and manage multiple links in a popup interface, then open them all at once with lazy loading tabs. Features link validation, duplicate removal, sorting, and persistent storage for later use.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="bulkLinkOpenEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Color Picker</h4>
                <p class="settings-item__description">Interactive color picking tool with EyeDropper API. Pick colors from any page, store them, and export as text or CSV. Appears as a Quick Action below Copy Element when enabled.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="colorPickerEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="custom-shortcut-container">
            <div class="custom-shortcut-label">
                <span style="color: #7C3AED; font-size: 18px;">●</span>
                Custom Global Shortcut
            </div>
            <div class="custom-shortcut-input-wrapper">
                <input 
                    type="text" 
                    id="colorpickerCustomShortcut" 
                    class="custom-shortcut-input" 
                    data-setting="colorpickerShortcut"
                    placeholder="Type shortcut (e.g., Ctrl+Shift+P)"
                    title="Press the keys you want to use as a shortcut. Examples: Ctrl+Shift+P, Alt+P, Ctrl+Cmd+P"
                />
                <div class="shortcut-help">
                    <span class="help-icon">ℹ️</span>
                    <div class="help-tooltip">
                        <strong>Supported formats:</strong><br>
                        • Ctrl+Shift+P<br>
                        • Alt+P<br>
                        • Ctrl+Cmd+P<br>
                        • Meta+Shift+P<br><br>
                        <strong>Modifiers:</strong> Ctrl, Alt, Shift, Meta/Cmd<br>
                        <strong>Keys:</strong> A-Z, 0-9
                    </div>
                </div>
            </div>
            <div class="shortcut-status" id="colorpickerShortcutStatus"></div>
        </div>
    </div>

    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Responsive Device Simulator</h4>
                <p class="settings-item__description">Test your website responsiveness across different devices including iPhones, Android phones, tablets, and desktops. Features device frame overlays, rotation, zoom controls, and keyboard shortcuts (Escape to close, R to rotate, +/- to zoom).</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="responsiveEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">SEO Tests</h4>
                <p class="settings-item__description">Comprehensive SEO testing toolkit including GTmetrix, YellowLab Tools, Google Structured Data Testing, Rich Results Testing, AMP Test, Mobile-Friendly Test, Google Cache, HTTP/2 Test, and SSL Checker. Opens a panel with checkboxes to select and run multiple tests simultaneously.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="seoTestsEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">Tracker Detection</h4>
                <p class="settings-item__description">Analyze the current page for tracking scripts and technologies. Detects Google Analytics, Facebook Pixel, advertising trackers, analytics tools, and other tracking technologies with detailed categorization and duplicate detection.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="trackerDetectionEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="settings-item">
        <div class="settings-item__content">
            <div class="settings-item__info">
                <h4 class="settings-item__title">YouTube Embed Scraper</h4>
                <p class="settings-item__description">Navigate to your YouTube channel's main videos page to scrape video embed codes, thumbnails, duration, views, and schema markup automatically. Configure a YouTube API key in General Settings to enable schema generation. Appears as a YouTube icon in the popup.<br><br>
                NOTE: After schema geenration you must DOUBLE CLICK inside the cell before copying and pasting the schema, if you do not, your schema will have double escape quotes on every entry("") and this will cause it to break.</p>
            </div>
            <div class="settings-item__control">
                <div class="toggle-switch toggle-switch--active" data-setting="youtubeEmbedScraperEnabled">
                    <div class="toggle-switch__track">
                        <div class="toggle-switch__thumb"></div>
                    </div>
                </div>
            </div>
        </div>
    </div></div> </div> </div> 